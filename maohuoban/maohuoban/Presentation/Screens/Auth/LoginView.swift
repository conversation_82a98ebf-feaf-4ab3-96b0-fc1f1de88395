//
//  LoginView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：登录页面 - 提供手机号验证码登录和密码登录功能
//  架构说明：遵循MVVM+组件化架构，使用LoginViewModel和组件化UI
//

import SwiftUI

// MARK: - 登录页面
struct LoginView: View {

    // MARK: - 依赖注入

    /// 认证ViewModel - 处理登录业务逻辑
    @ObservedObject var authViewModel: AuthViewModel

    /// 登录ViewModel - 处理登录页面状态
    @StateObject private var loginViewModel: LoginViewModel

    /// 关闭弹框
    @Environment(\.presentationMode) var presentationMode

    // MARK: - 初始化

    /// 初始化登录页面
    /// - Parameter authViewModel: 认证ViewModel实例
    init(authViewModel: AuthViewModel) {
        self.authViewModel = authViewModel
        self._loginViewModel = StateObject(wrappedValue: LoginViewModel(authViewModel: authViewModel))
    }

    // MARK: - 视图主体

    var body: some View {
        NavigationView {
            ZStack {
                // 背景色
                MHTheme.backgroundColor
                    .ignoresSafeArea()

                // 主要内容
                ScrollView {
                    VStack(spacing: 32) {
                        // 顶部间距
                        Spacer()
                            .frame(height: 40)

                        // 标题区域
                        titleSection

                        // 副标题（仅验证码登录时显示）
                        if loginViewModel.loginMethod == .verification {
                            subtitleSection
                        }

                        // 输入区域
                        inputSection

                        // 动态按钮区域
                        actionButtonSection

                        // 协议同意区域
                        termsSection

                        // 底部间距
                        Spacer()
                            .frame(height: 40)
                    }
                    .padding(.horizontal, 24)
                }
                .scrollIndicators(.hidden) // 隐藏滚动条
            }
            .navigationBarHidden(true)
        }
        .onDisappear {
            // 页面消失时清理资源
            // LoginViewModel的deinit会自动清理定时器
        }
        .sheet(isPresented: $loginViewModel.showTermsSheet) {
            // 协议同意底部弹框
            TermsSheetView(
                onAgreeAndContinue: {
                    loginViewModel.agreeTermsAndContinue()
                },
                onUserAgreementTap: {
                    showUserAgreement()
                },
                onPrivacyPolicyTap: {
                    showPrivacyPolicy()
                },
                onMinorProtectionTap: {
                    showMinorProtectionRules()
                }
            )
        }
    }

    // MARK: - 子视图组件

    /// 标题区域 - 根据登录方式动态显示
    private var titleSection: some View {
        Text(loginViewModel.loginMethod == .password ? "密码登录" : "手机号登录")
            .font(.system(size: 24, weight: .medium))
            .foregroundColor(MHTheme.primaryTextColor)
    }

    /// 副标题区域 - 仅在验证码登录时显示，提示用户自动注册功能
    private var subtitleSection: some View {
        Text("未注册的手机号登录后将自动注册")
            .font(.system(size: 14))
            .foregroundColor(MHTheme.secondaryTextColor)
    }

    /// 输入区域 - 根据登录方式显示不同输入框
    private var inputSection: some View {
        VStack(spacing: 24) {
            // 手机号输入区域
            PhoneInputView(phoneNumber: $loginViewModel.phoneNumber)

            // 根据登录方式显示不同的输入框
            if loginViewModel.loginMethod == .password {
                // 密码输入区域
                PasswordInputView(password: $loginViewModel.password)
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .animation(.easeInOut(duration: 0.3), value: loginViewModel.loginMethod)
            } else if loginViewModel.isCodeSent {
                // 验证码输入区域（仅在发送验证码后显示）
                VerificationCodeView(
                    verificationCode: $loginViewModel.verificationCode,
                    countdown: $loginViewModel.countdown,
                    isLoading: loginViewModel.isLoading,
                    isValidPhoneNumber: loginViewModel.isValidPhoneNumber,
                    onResendCode: {
                        loginViewModel.handleSendVerificationCode()
                    }
                )
                .transition(.move(edge: .top).combined(with: .opacity))
                .animation(.easeInOut(duration: 0.3), value: loginViewModel.isCodeSent)
            }

            // 登录方式切换按钮（始终在最后一个输入框下面）
            LoginMethodSwitchView(
                currentMethod: loginViewModel.loginMethod,
                onToggle: {
                    loginViewModel.toggleLoginMethod()
                },
                onPhoneUnavailable: {
                    loginViewModel.handlePhoneNumberUnavailable()
                }
            )
        }
    }

    /// 动态按钮区域 - 根据登录方式和状态显示不同按钮
    private var actionButtonSection: some View {
        VStack(spacing: 16) {
            if loginViewModel.loginMethod == .password {
                // 密码登录时直接显示登录按钮
                passwordLoginButton
            } else if !loginViewModel.isCodeSent {
                // 验证码登录且未发送验证码时显示发送验证码按钮
                sendCodeButton
            } else {
                // 验证码登录且已发送验证码时显示登录按钮
                verificationLoginButton
            }
        }
    }

    /// 协议同意区域
    private var termsSection: some View {
        TermsAgreementView(
            isAgreed: $loginViewModel.isAgreedToTerms,
            onUserAgreementTap: {
                showUserAgreement()
            },
            onPrivacyPolicyTap: {
                showPrivacyPolicy()
            },
            onMinorProtectionTap: {
                showMinorProtectionRules()
            }
        )
    }

    // MARK: - 按钮组件

    /// 发送验证码按钮
    private var sendCodeButton: some View {
        Button(action: {
            loginViewModel.handleSendVerificationCode()
        }) {
            HStack {
                if loginViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(loginViewModel.isLoading ? "发送中..." : "获取验证码")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(MHTheme.primaryColor)
                    .opacity(loginViewModel.isLoading || !loginViewModel.isSendCodeFormValid ? 0.5 : 1.0)
            )
        }
        .disabled(loginViewModel.isLoading || !loginViewModel.isSendCodeFormValid)
    }

    /// 验证码登录按钮
    private var verificationLoginButton: some View {
        Button(action: {
            loginViewModel.handleVerificationLogin()
        }) {
            HStack {
                if loginViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(loginViewModel.isLoading ? "登录中..." : "验证并登录")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(MHTheme.primaryColor)
                    .opacity(loginViewModel.isLoading || !loginViewModel.isVerificationFormValid ? 0.5 : 1.0)
            )
        }
        .disabled(loginViewModel.isLoading || !loginViewModel.isVerificationFormValid)
    }

    /// 密码登录按钮
    private var passwordLoginButton: some View {
        Button(action: {
            loginViewModel.handlePasswordLogin()
        }) {
            HStack {
                if loginViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(loginViewModel.isLoading ? "登录中..." : "登录")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(MHTheme.primaryColor)
                    .opacity(loginViewModel.isLoading || !loginViewModel.isPasswordFormValid ? 0.5 : 1.0)
            )
        }
        .disabled(loginViewModel.isLoading || !loginViewModel.isPasswordFormValid)
    }

    // MARK: - 私有方法

    /// 显示用户协议
    private func showUserAgreement() {
        print("👤 显示用户协议")
        // TODO: 实现用户协议页面导航或弹框
    }

    /// 显示隐私政策
    private func showPrivacyPolicy() {
        print("🔒 显示隐私政策")
        // TODO: 实现隐私政策页面导航或弹框
    }

    /// 显示未成年人保护规则
    private func showMinorProtectionRules() {
        print("👶 显示未成年人个人信息保护规则")
        // TODO: 实现未成年人保护规则页面导航或弹框
    }
}

// MARK: - 预览
#Preview {
    LoginView(authViewModel: AuthViewModel())
}
