//
//  PasswordInputView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：密码输入组件 - 负责密码输入和显示/隐藏切换
//  架构说明：遵循组件化设计，可复用的UI组件
//

import SwiftUI

// MARK: - 密码输入组件
/// 密码输入组件
/// 包含密码输入框和显示/隐藏切换按钮，使用下划线样式
struct PasswordInputView: View {
    
    // MARK: - 绑定属性
    
    /// 密码输入绑定
    @Binding var password: String
    
    /// 是否显示密码
    @State private var isPasswordVisible: Bool = false
    
    /// 占位符文本
    let placeholder: String
    
    /// 最大长度限制
    let maxLength: Int
    
    // MARK: - 初始化
    
    /// 初始化密码输入组件
    /// - Parameters:
    ///   - password: 密码绑定
    ///   - placeholder: 占位符文本
    ///   - maxLength: 最大长度限制
    init(
        password: Binding<String>,
        placeholder: String = "请输入密码",
        maxLength: Int = 20
    ) {
        self._password = password
        self.placeholder = placeholder
        self.maxLength = maxLength
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 密码输入框和显示/隐藏按钮
            HStack(spacing: 12) {
                // 密码输入框
                passwordInputSection
                
                // 显示/隐藏密码按钮
                toggleVisibilityButton
            }
            
            // 下划线
            underlineSection
        }
    }
    
    // MARK: - 子视图组件
    
    /// 密码输入框
    private var passwordInputSection: some View {
        Group {
            if isPasswordVisible {
                // 显示密码时使用普通文本框
                TextField(placeholder, text: $password)
                    .font(.system(size: 16))
                    .foregroundColor(MHTheme.primaryTextColor)
            } else {
                // 隐藏密码时使用安全文本框
                SecureField(placeholder, text: $password)
                    .font(.system(size: 16))
                    .foregroundColor(MHTheme.primaryTextColor)
            }
        }
        .onChange(of: password) { newValue in
            // 限制密码长度
            if newValue.count > maxLength {
                password = String(newValue.prefix(maxLength))
            }
        }
    }
    
    /// 显示/隐藏密码切换按钮
    private var toggleVisibilityButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                isPasswordVisible.toggle()
            }
        }) {
            Image(systemName: isPasswordVisible ? "eye.slash" : "eye")
                .font(.system(size: 16))
                .foregroundColor(MHTheme.secondaryTextColor)
        }
        .frame(width: 24, height: 24)
    }
    
    /// 下划线区域
    private var underlineSection: some View {
        Rectangle()
            .fill(password.isEmpty ? MHTheme.separatorColor : MHTheme.primaryColor)
            .frame(height: 1)
            .animation(.easeInOut(duration: 0.2), value: password.isEmpty)
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 30) {
        // 空密码状态
        PasswordInputView(
            password: .constant(""),
            placeholder: "请输入密码"
        )
        
        // 有密码状态
        PasswordInputView(
            password: .constant("123456"),
            placeholder: "请输入密码"
        )
        
        // 确认密码输入框
        PasswordInputView(
            password: .constant(""),
            placeholder: "请确认密码"
        )
    }
    .padding()
    .background(MHTheme.backgroundColor)
}
