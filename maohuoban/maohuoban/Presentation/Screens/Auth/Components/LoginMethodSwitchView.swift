//
//  LoginMethodSwitchView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：登录方式切换组件 - 负责验证码登录和密码登录之间的切换
//  架构说明：遵循组件化设计，可复用的UI组件
//

import SwiftUI

// MARK: - 登录方式切换组件
/// 登录方式切换组件
/// 提供验证码登录和密码登录之间的切换功能
struct LoginMethodSwitchView: View {
    
    // MARK: - 绑定属性
    
    /// 当前登录方式
    let currentMethod: LoginMethod
    
    /// 切换登录方式回调
    let onToggle: () -> Void
    
    /// 号码不可用点击回调
    let onPhoneUnavailable: () -> Void
    
    // MARK: - 初始化
    
    /// 初始化登录方式切换组件
    /// - Parameters:
    ///   - currentMethod: 当前登录方式
    ///   - onToggle: 切换登录方式回调
    ///   - onPhoneUnavailable: 号码不可用点击回调
    init(
        currentMethod: LoginMethod,
        onToggle: @escaping () -> Void,
        onPhoneUnavailable: @escaping () -> Void = {}
    ) {
        self.currentMethod = currentMethod
        self.onToggle = onToggle
        self.onPhoneUnavailable = onPhoneUnavailable
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        HStack {
            // 登录方式切换按钮
            switchMethodSection
            
            Spacer()
            
            // 号码不可用按钮
            phoneUnavailableSection
        }
    }
    
    // MARK: - 子视图组件
    
    /// 登录方式切换区域
    private var switchMethodSection: some View {
        HStack(spacing: 6) {
            // 图标
            Image(systemName: switchMethodIcon)
                .font(.system(size: 14))
                .foregroundColor(MHTheme.primaryColor)
            
            // 切换按钮
            Button(switchMethodText) {
                onToggle()
            }
            .font(.system(size: 14))
            .foregroundColor(MHTheme.primaryColor)
        }
    }
    
    /// 号码不可用区域
    private var phoneUnavailableSection: some View {
        Button("号码不可用") {
            onPhoneUnavailable()
        }
        .font(.system(size: 14))
        .foregroundColor(MHTheme.tertiaryTextColor)
    }
    
    // MARK: - 计算属性
    
    /// 切换方式图标
    private var switchMethodIcon: String {
        switch currentMethod {
        case .verification:
            return "lock"      // 当前是验证码登录，显示锁图标（切换到密码登录）
        case .password:
            return "message"   // 当前是密码登录，显示消息图标（切换到验证码登录）
        }
    }
    
    /// 切换方式文本
    private var switchMethodText: String {
        switch currentMethod {
        case .verification:
            return "密码登录"   // 当前是验证码登录，显示"密码登录"
        case .password:
            return "验证码登录" // 当前是密码登录，显示"验证码登录"
        }
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 30) {
        // 验证码登录状态
        LoginMethodSwitchView(
            currentMethod: .verification,
            onToggle: {
                print("切换到密码登录")
            },
            onPhoneUnavailable: {
                print("号码不可用")
            }
        )
        
        // 密码登录状态
        LoginMethodSwitchView(
            currentMethod: .password,
            onToggle: {
                print("切换到验证码登录")
            },
            onPhoneUnavailable: {
                print("号码不可用")
            }
        )
    }
    .padding()
    .background(MHTheme.backgroundColor)
}
