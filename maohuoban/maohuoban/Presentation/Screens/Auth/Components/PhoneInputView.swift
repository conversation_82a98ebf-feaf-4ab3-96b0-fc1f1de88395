//
//  PhoneInputView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：手机号输入组件 - 负责手机号输入和国家代码选择
//  架构说明：遵循组件化设计，可复用的UI组件
//

import SwiftUI

// MARK: - 手机号输入组件
/// 手机号输入组件
/// 包含国家代码选择器和手机号输入框，使用下划线样式
struct PhoneInputView: View {
    
    // MARK: - 绑定属性
    
    /// 手机号输入绑定
    @Binding var phoneNumber: String
    
    /// 国家代码（默认+86）
    @State private var countryCode: String = "+86"
    
    // MARK: - 视图主体
    
    var body: some View {
        // 国家代码和手机号输入
        HStack(spacing: 12) {
            // 国家代码选择器
            countryCodeSection
            
            // 手机号输入框
            phoneNumberInputSection
        }
    }
    
    // MARK: - 子视图组件
    
    /// 国家代码选择器 - 下划线样式
    private var countryCodeSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(action: {
                // TODO: 实现国家代码选择功能
                handleCountryCodeSelection()
            }) {
                HStack(spacing: 4) {
                    Text(countryCode)
                        .font(.system(size: 16))
                        .foregroundColor(MHTheme.primaryTextColor)
                    
                    Image(systemName: "chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(MHTheme.secondaryTextColor)
                }
            }
            
            // 下划线
            Rectangle()
                .fill(MHTheme.separatorColor)
                .frame(height: 1)
        }
        .frame(width: 60)
    }
    
    /// 手机号输入框 - 下划线样式
    private var phoneNumberInputSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            TextField("请输入手机号", text: $phoneNumber)
                .font(.system(size: 16))
                .keyboardType(.phonePad)
                .foregroundColor(MHTheme.primaryTextColor)
                .onChange(of: phoneNumber) { newValue in
                    // 限制手机号长度为11位
                    if newValue.count > 11 {
                        phoneNumber = String(newValue.prefix(11))
                    }
                }
            
            // 下划线 - 根据输入状态变化颜色
            Rectangle()
                .fill(phoneNumber.isEmpty ? MHTheme.separatorColor : MHTheme.primaryColor)
                .frame(height: 1)
                .animation(.easeInOut(duration: 0.2), value: phoneNumber.isEmpty)
        }
    }
    
    // MARK: - 私有方法
    
    /// 处理国家代码选择
    private func handleCountryCodeSelection() {
        print("🌍 选择国家代码")
        // TODO: 实现国家代码选择功能
        // 可能的实现方案：
        // 1. 显示国家代码选择器弹框
        // 2. 跳转到国家代码选择页面
        // 3. 使用ActionSheet显示常用国家代码
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        PhoneInputView(phoneNumber: .constant(""))
        
        PhoneInputView(phoneNumber: .constant("13812345678"))
    }
    .padding()
    .background(MHTheme.backgroundColor)
}
