//
//  TermsSheetView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：协议同意底部弹框组件 - 当用户未同意协议时显示的底部弹框
//  架构说明：遵循组件化设计，可复用的UI组件
//

import SwiftUI

// MARK: - 协议同意底部弹框组件
/// 协议同意底部弹框组件
/// 当用户未同意协议时弹出，引导用户阅读并同意相关条款
struct TermsSheetView: View {
    
    // MARK: - 回调属性
    
    /// 同意并继续回调
    let onAgreeAndContinue: () -> Void
    
    /// 协议链接点击回调
    let onUserAgreementTap: () -> Void
    let onPrivacyPolicyTap: () -> Void
    let onMinorProtectionTap: () -> Void
    
    // MARK: - 初始化
    
    /// 初始化协议同意底部弹框
    /// - Parameters:
    ///   - onAgreeAndContinue: 同意并继续回调
    ///   - onUserAgreementTap: 用户协议点击回调
    ///   - onPrivacyPolicyTap: 隐私政策点击回调
    ///   - onMinorProtectionTap: 未成年人保护规则点击回调
    init(
        onAgreeAndContinue: @escaping () -> Void,
        onUserAgreementTap: @escaping () -> Void = { print("👤 显示用户协议") },
        onPrivacyPolicyTap: @escaping () -> Void = { print("🔒 显示隐私政策") },
        onMinorProtectionTap: @escaping () -> Void = { print("👶 显示未成年人保护规则") }
    ) {
        self.onAgreeAndContinue = onAgreeAndContinue
        self.onUserAgreementTap = onUserAgreementTap
        self.onPrivacyPolicyTap = onPrivacyPolicyTap
        self.onMinorProtectionTap = onMinorProtectionTap
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(spacing: 0) {
            VStack(spacing: 24) {
                // 标题
                titleSection
                
                // 协议链接
                termsLinksSection
                
                // 同意并继续按钮
                agreeButtonSection
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 24)
        }
        .background(backgroundSection)
        .presentationDetents([.height(200)])
        .presentationDragIndicator(.hidden)
    }
    
    // MARK: - 子视图组件
    
    /// 标题区域
    private var titleSection: some View {
        Text("请阅读并同意以下条款")
            .font(.system(size: 18, weight: .medium))
            .foregroundColor(MHTheme.primaryTextColor)
            .padding(.top, 24)
    }
    
    /// 协议链接区域
    private var termsLinksSection: some View {
        HStack(spacing: 4) {
            Button("《用户协议》") {
                onUserAgreementTap()
            }
            .font(.system(size: 12))
            .foregroundColor(MHTheme.primaryColor)
            
            Button("《隐私政策》") {
                onPrivacyPolicyTap()
            }
            .font(.system(size: 12))
            .foregroundColor(MHTheme.primaryColor)
            
            Button("《未成年人个人信息保护规则》") {
                onMinorProtectionTap()
            }
            .font(.system(size: 12))
            .foregroundColor(MHTheme.primaryColor)
        }
    }
    
    /// 同意并继续按钮区域
    private var agreeButtonSection: some View {
        Button(action: {
            onAgreeAndContinue()
        }) {
            Text("同意并继续")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(MHTheme.primaryColor)
                )
        }
        .padding(.horizontal, 20)
    }
    
    /// 背景区域
    private var backgroundSection: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(MHTheme.backgroundColor)
            .ignoresSafeArea(edges: .bottom)
    }
}

// MARK: - 预览
#Preview {
    // 使用sheet预览
    struct PreviewWrapper: View {
        @State private var showSheet = true
        
        var body: some View {
            VStack {
                Button("显示协议弹框") {
                    showSheet = true
                }
            }
            .sheet(isPresented: $showSheet) {
                TermsSheetView(
                    onAgreeAndContinue: {
                        print("用户同意并继续")
                        showSheet = false
                    },
                    onUserAgreementTap: {
                        print("点击用户协议")
                    },
                    onPrivacyPolicyTap: {
                        print("点击隐私政策")
                    },
                    onMinorProtectionTap: {
                        print("点击未成年人保护规则")
                    }
                )
            }
        }
    }
    
    return PreviewWrapper()
}
