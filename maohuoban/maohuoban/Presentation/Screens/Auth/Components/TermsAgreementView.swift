//
//  TermsAgreementView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：协议同意组件 - 负责用户协议同意的UI展示
//  架构说明：遵循组件化设计，可复用的UI组件
//

import SwiftUI

// MARK: - 协议同意组件
/// 协议同意组件
/// 包含复选框和协议链接，用于用户同意相关条款
struct TermsAgreementView: View {
    
    // MARK: - 绑定属性
    
    /// 是否同意协议绑定
    @Binding var isAgreed: Bool
    
    /// 协议链接点击回调
    let onUserAgreementTap: () -> Void
    let onPrivacyPolicyTap: () -> Void
    let onMinorProtectionTap: () -> Void
    
    // MARK: - 初始化
    
    /// 初始化协议同意组件
    /// - Parameters:
    ///   - isAgreed: 是否同意协议绑定
    ///   - onUserAgreementTap: 用户协议点击回调
    ///   - onPrivacyPolicyTap: 隐私政策点击回调
    ///   - onMinorProtectionTap: 未成年人保护规则点击回调
    init(
        isAgreed: Binding<Bool>,
        onUserAgreementTap: @escaping () -> Void = {},
        onPrivacyPolicyTap: @escaping () -> Void = {},
        onMinorProtectionTap: @escaping () -> Void = {}
    ) {
        self._isAgreed = isAgreed
        self.onUserAgreementTap = onUserAgreementTap
        self.onPrivacyPolicyTap = onPrivacyPolicyTap
        self.onMinorProtectionTap = onMinorProtectionTap
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            // 复选框
            checkboxSection
            
            // 协议文本和链接
            termsTextSection
        }
    }
    
    // MARK: - 子视图组件
    
    /// 复选框区域
    private var checkboxSection: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                isAgreed.toggle()
            }
        }) {
            Image(systemName: isAgreed ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 16))
                .foregroundColor(isAgreed ? MHTheme.primaryColor : MHTheme.tertiaryTextColor)
        }
        .padding(.top, 2) // 与文本对齐
    }
    
    /// 协议文本区域
    private var termsTextSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 第一行：我已阅读并同意
            HStack(spacing: 0) {
                Text("我已阅读并同意")
                    .font(.system(size: 12))
                    .foregroundColor(MHTheme.tertiaryTextColor)
                
                // 用户协议链接
                Button("《用户协议》") {
                    onUserAgreementTap()
                }
                .font(.system(size: 12))
                .foregroundColor(MHTheme.primaryColor)
                
                // 隐私政策链接
                Button("《隐私政策》") {
                    onPrivacyPolicyTap()
                }
                .font(.system(size: 12))
                .foregroundColor(MHTheme.primaryColor)
            }
            
            // 第二行：未成年人保护规则
            Button("《未成年人个人信息保护规则》") {
                onMinorProtectionTap()
            }
            .font(.system(size: 12))
            .foregroundColor(MHTheme.primaryColor)
        }
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 30) {
        // 未同意状态
        TermsAgreementView(
            isAgreed: .constant(false),
            onUserAgreementTap: {
                print("点击用户协议")
            },
            onPrivacyPolicyTap: {
                print("点击隐私政策")
            },
            onMinorProtectionTap: {
                print("点击未成年人保护规则")
            }
        )
        
        // 已同意状态
        TermsAgreementView(
            isAgreed: .constant(true),
            onUserAgreementTap: {
                print("点击用户协议")
            },
            onPrivacyPolicyTap: {
                print("点击隐私政策")
            },
            onMinorProtectionTap: {
                print("点击未成年人保护规则")
            }
        )
    }
    .padding()
    .background(MHTheme.backgroundColor)
}
