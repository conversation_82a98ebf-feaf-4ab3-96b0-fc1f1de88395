//
//  VerificationCodeView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：验证码输入组件 - 负责验证码输入和倒计时显示
//  架构说明：遵循组件化设计，可复用的UI组件
//

import SwiftUI

// MARK: - 验证码输入组件
/// 验证码输入组件
/// 包含验证码输入框和倒计时/重新获取按钮，使用下划线样式
struct VerificationCodeView: View {
    
    // MARK: - 绑定属性
    
    /// 验证码输入绑定
    @Binding var verificationCode: String
    
    /// 倒计时秒数绑定
    @Binding var countdown: Int
    
    /// 是否正在加载
    let isLoading: Bool
    
    /// 手机号是否有效
    let isValidPhoneNumber: Bool
    
    /// 重新获取验证码回调
    let onResendCode: () -> Void
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 验证码输入框和倒计时/重新获取按钮
            HStack(spacing: 12) {
                // 验证码输入框
                verificationCodeInputSection
                
                // 倒计时或重新获取按钮
                countdownOrResendSection
            }
            
            // 下划线
            underlineSection
        }
    }
    
    // MARK: - 子视图组件
    
    /// 验证码输入框
    private var verificationCodeInputSection: some View {
        TextField("请输入验证码", text: $verificationCode)
            .font(.system(size: 16))
            .keyboardType(.numberPad)
            .foregroundColor(MHTheme.primaryTextColor)
            .onChange(of: verificationCode) { newValue in
                // 限制验证码长度为6位
                if newValue.count > 6 {
                    verificationCode = String(newValue.prefix(6))
                }
            }
    }
    
    /// 倒计时或重新获取按钮区域
    private var countdownOrResendSection: some View {
        Group {
            if countdown > 0 {
                // 显示倒计时
                countdownSection
            } else {
                // 显示重新获取按钮
                resendButtonSection
            }
        }
    }
    
    /// 倒计时显示
    private var countdownSection: some View {
        Text("\(countdown)s")
            .font(.system(size: 14))
            .foregroundColor(MHTheme.secondaryTextColor)
            .frame(minWidth: 40)
    }
    
    /// 重新获取按钮
    private var resendButtonSection: some View {
        Button("重新获取") {
            onResendCode()
        }
        .font(.system(size: 14))
        .foregroundColor(MHTheme.primaryColor)
        .disabled(isLoading || !isValidPhoneNumber)
    }
    
    /// 下划线区域
    private var underlineSection: some View {
        Rectangle()
            .fill(verificationCode.isEmpty ? MHTheme.separatorColor : MHTheme.primaryColor)
            .frame(height: 1)
            .animation(.easeInOut(duration: 0.2), value: verificationCode.isEmpty)
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 30) {
        // 倒计时状态
        VerificationCodeView(
            verificationCode: .constant(""),
            countdown: .constant(45),
            isLoading: false,
            isValidPhoneNumber: true,
            onResendCode: {
                print("重新发送验证码")
            }
        )
        
        // 可重新获取状态
        VerificationCodeView(
            verificationCode: .constant("123456"),
            countdown: .constant(0),
            isLoading: false,
            isValidPhoneNumber: true,
            onResendCode: {
                print("重新发送验证码")
            }
        )
        
        // 加载状态
        VerificationCodeView(
            verificationCode: .constant(""),
            countdown: .constant(0),
            isLoading: true,
            isValidPhoneNumber: true,
            onResendCode: {
                print("重新发送验证码")
            }
        )
    }
    .padding()
    .background(MHTheme.backgroundColor)
}
