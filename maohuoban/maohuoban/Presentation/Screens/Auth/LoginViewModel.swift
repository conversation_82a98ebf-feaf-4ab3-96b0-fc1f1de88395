//
//  LoginViewModel.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：登录页面专用ViewModel - 处理登录相关的UI状态和业务逻辑
//  架构说明：遵循MVVM模式，专门负责登录页面的状态管理
//

import Foundation
import Combine
import SwiftUI

// MARK: - 登录方式枚举
/// 登录方式
enum LoginMethod {
    case verification  // 验证码登录
    case password      // 密码登录
}

// MARK: - 待执行操作类型
/// 待执行操作类型
enum PendingAction {
    case sendVerificationCode  // 发送验证码
    case verificationLogin     // 验证码登录
    case passwordLogin         // 密码登录
}

// MARK: - 登录ViewModel
/// 登录页面专用ViewModel
/// 负责管理登录页面的UI状态和用户交互逻辑
class LoginViewModel: ObservableObject {

    // MARK: - 发布属性

    /// 手机号输入
    @Published var phoneNumber: String = ""

    /// 验证码输入
    @Published var verificationCode: String = ""

    /// 密码输入
    @Published var password: String = ""

    /// 当前登录方式
    @Published var loginMethod: LoginMethod = .verification

    /// 是否已发送验证码
    @Published var isCodeSent: Bool = false

    /// 验证码倒计时
    @Published var countdown: Int = 0

    /// 是否同意协议
    @Published var isAgreedToTerms: Bool = false

    /// 是否显示协议同意底部弹框
    @Published var showTermsSheet: Bool = false

    /// 待执行的操作类型
    @Published var pendingAction: PendingAction? = nil

    // MARK: - 私有属性

    /// 倒计时定时器
    private var timer: Timer?

    /// 认证ViewModel - 处理实际的登录业务逻辑
    private let authViewModel: AuthViewModel

    /// Combine订阅集合
    private var cancellables = Set<AnyCancellable>()

    // MARK: - 初始化

    /// 初始化登录ViewModel
    /// - Parameter authViewModel: 认证ViewModel实例
    init(authViewModel: AuthViewModel) {
        self.authViewModel = authViewModel
        setupBindings()
    }

    // MARK: - 计算属性

    /// 手机号是否有效
    var isValidPhoneNumber: Bool {
        phoneNumber.count == 11 && phoneNumber.hasPrefix("1")
    }

    /// 发送验证码表单是否有效
    var isSendCodeFormValid: Bool {
        isValidPhoneNumber
    }

    /// 验证码登录表单是否有效
    var isVerificationFormValid: Bool {
        isValidPhoneNumber &&
        !verificationCode.isEmpty &&
        verificationCode.count == 6
    }

    /// 密码登录表单是否有效
    var isPasswordFormValid: Bool {
        isValidPhoneNumber &&
        !password.isEmpty &&
        password.count >= 6
    }

    /// 是否正在加载
    var isLoading: Bool {
        authViewModel.isLoading
    }

    /// 错误信息
    var errorMessage: String? {
        authViewModel.errorMessage
    }

    /// 是否已认证
    var isAuthenticated: Bool {
        authViewModel.isAuthenticated
    }

    // MARK: - 公共方法

    /// 切换登录方式
    func toggleLoginMethod() {
        withAnimation(.easeInOut(duration: 0.3)) {
            loginMethod = loginMethod == .verification ? .password : .verification

            // 切换时重置相关状态
            if loginMethod == .password {
                // 切换到密码登录时，重置验证码相关状态
                resetVerificationState()
            } else {
                // 切换到验证码登录时，重置密码
                password = ""
            }
        }

        print("🔄 切换到\(loginMethod == .password ? "密码" : "验证码")登录")
    }

    /// 处理发送验证码请求
    func handleSendVerificationCode() {
        guard isAgreedToTerms else {
            pendingAction = .sendVerificationCode
            showTermsSheet = true
            return
        }

        sendVerificationCode()
    }

    /// 处理验证码登录请求
    func handleVerificationLogin() {
        guard isAgreedToTerms else {
            pendingAction = .verificationLogin
            showTermsSheet = true
            return
        }

        authViewModel.login(username: phoneNumber, password: verificationCode)
    }

    /// 处理密码登录请求
    func handlePasswordLogin() {
        guard isAgreedToTerms else {
            pendingAction = .passwordLogin
            showTermsSheet = true
            return
        }

        authViewModel.login(username: phoneNumber, password: password)
    }

    /// 同意协议并执行待处理操作
    func agreeTermsAndContinue() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isAgreedToTerms = true
            showTermsSheet = false
        }

        // 延迟执行待处理操作，确保弹框关闭动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.executePendingAction()
        }
    }

    /// 清除错误信息
    func clearError() {
        authViewModel.clearError()
    }

    /// 处理号码不可用功能
    func handlePhoneNumberUnavailable() {
        print("📞 号码不可用功能被点击")
        // TODO: 实现号码不可用功能
        // 可能的实现方案：
        // 1. 跳转到客服页面或联系方式
        // 2. 显示帮助说明弹框
        // 3. 跳转到其他登录方式页面
        // 4. 显示号码申诉或找回功能
        // 5. 跳转到注册新号码页面
    }

    // MARK: - 私有方法

    /// 设置数据绑定
    private func setupBindings() {
        // 监听认证状态变化，在这里可以添加登录成功后的处理逻辑
        authViewModel.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    // 登录成功，可以在这里添加额外的处理逻辑
                    print("✅ 登录成功")
                }
            }
            .store(in: &cancellables)
    }

    /// 发送验证码
    private func sendVerificationCode() {
        guard isValidPhoneNumber else {
            print("❌ 手机号格式不正确")
            return
        }

        print("📱 发送验证码到：\(phoneNumber)")
        isCodeSent = true
        countdown = 60

        // 启动倒计时
        startCountdown()
    }

    /// 启动倒计时
    private func startCountdown() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }

            if self.countdown > 0 {
                self.countdown -= 1
            } else {
                self.timer?.invalidate()
                self.timer = nil
            }
        }
    }

    /// 重置验证码相关状态
    private func resetVerificationState() {
        isCodeSent = false
        verificationCode = ""
        timer?.invalidate()
        timer = nil
        countdown = 0
    }

    /// 执行待处理的操作
    private func executePendingAction() {
        guard let action = pendingAction else { return }

        switch action {
        case .sendVerificationCode:
            sendVerificationCode()
        case .verificationLogin:
            authViewModel.login(username: phoneNumber, password: verificationCode)
        case .passwordLogin:
            authViewModel.login(username: phoneNumber, password: password)
        }

        // 清除待处理操作
        pendingAction = nil
    }

    // MARK: - 清理资源

    deinit {
        timer?.invalidate()
        timer = nil
    }
}
