//
//  ProfileView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：个人资料视图
//  重构说明：从HomeView.swift中分离出来，实现Profile模块的独立管理
//  架构模式：MVVM - 这是View层，后续会添加对应的ViewModel
//

import SwiftUI

// MARK: - 个人资料视图
/// 个人资料页面 - 显示用户信息、设置选项、统计数据等
/// 包含个人信息编辑、隐私设置、账户管理等功能
/// 支持主题切换、语言设置、通知管理等个性化配置
struct ProfileView: View {

    // MARK: - 状态变量
    /// 是否显示设置页面
    @State private var showSettings = false

    /// 是否显示编辑资料页面
    @State private var showEditProfile = false

    /// 用户头像（模拟数据）
    @State private var userAvatar = "person.circle.fill"

    /// 用户名（模拟数据）
    @State private var userName = "毛伙伴用户"

    /// 用户简介（模拟数据）
    @State private var userBio = "这里是用户的个人简介..."

    // MARK: - 模拟统计数据
    /// 关注数
    @State private var followingCount = 128

    /// 粉丝数
    @State private var followersCount = 256

    /// 动态数
    @State private var postsCount = 42

    /// 获赞数
    @State private var likesCount = 1024

    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // MARK: 用户信息区域
                    createUserInfoSection()

                    // MARK: 统计数据区域
                    createStatsSection()

                    // MARK: 功能菜单区域
                    createMenuSection()

                    // MARK: 设置区域
                    createSettingsSection()
                }
                .padding(.horizontal, 16)
                .padding(.top, 20)
            }
            .scrollIndicators(.hidden) // 隐藏滚动指示器
            .navigationTitle("我的")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showSettings = true
                    }) {
                        Image(systemName: "gearshape")
                            .foregroundColor(MHTheme.primaryTextColor)
                    }
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            // TODO: 设置页面
            Text("设置页面")
        }
        .sheet(isPresented: $showEditProfile) {
            // TODO: 编辑资料页面
            Text("编辑资料页面")
        }
        .onAppear {
            // 视图出现时的初始化操作
            setupProfileView()
        }
    }

    // MARK: - 私有方法

    /// 创建用户信息区域
    /// - Returns: 用户信息视图
    private func createUserInfoSection() -> some View {
        VStack(spacing: 16) {
            // 用户头像
            Button(action: {
                showEditProfile = true
            }) {
                Image(systemName: userAvatar)
                    .font(.system(size: 80))
                    .foregroundColor(MHTheme.primaryColor)
                    .frame(width: 100, height: 100)
                    .background(MHTheme.lightGrayColor)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(MHTheme.borderColor, lineWidth: 2)
                    )
            }

            // 用户名和简介
            VStack(spacing: 8) {
                Text(userName)
                    .font(MHTheme.titleFont)
                    .foregroundColor(MHTheme.primaryTextColor)

                Text(userBio)
                    .font(MHTheme.bodyFont)
                    .foregroundColor(MHTheme.secondaryTextColor)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }

            // 编辑资料按钮
            Button(action: {
                showEditProfile = true
            }) {
                Text("编辑资料")
                    .font(MHTheme.bodyFont)
                    .foregroundColor(MHTheme.primaryColor)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: MHTheme.cornerRadiusM)
                            .stroke(MHTheme.primaryColor, lineWidth: 1)
                    )
            }
        }
        .padding(20)
        .background(MHTheme.secondaryBackgroundColor)
        .cornerRadius(MHTheme.cornerRadiusL)
    }

    /// 创建统计数据区域
    /// - Returns: 统计数据视图
    private func createStatsSection() -> some View {
        HStack(spacing: 0) {
            createStatItem(title: "关注", count: followingCount)
            createStatItem(title: "粉丝", count: followersCount)
            createStatItem(title: "动态", count: postsCount)
            createStatItem(title: "获赞", count: likesCount)
        }
        .padding(.vertical, 16)
        .background(MHTheme.secondaryBackgroundColor)
        .cornerRadius(MHTheme.cornerRadiusL)
    }

    /// 创建统计项
    /// - Parameters:
    ///   - title: 标题
    ///   - count: 数量
    /// - Returns: 统计项视图
    private func createStatItem(title: String, count: Int) -> some View {
        Button(action: {
            handleStatTap(title: title)
        }) {
            VStack(spacing: 4) {
                Text("\(count)")
                    .font(MHTheme.titleFont)
                    .foregroundColor(MHTheme.primaryTextColor)

                Text(title)
                    .font(MHTheme.captionFont)
                    .foregroundColor(MHTheme.secondaryTextColor)
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
    }

    /// 创建功能菜单区域
    /// - Returns: 功能菜单视图
    private func createMenuSection() -> some View {
        VStack(spacing: 0) {
            createMenuItem(icon: "heart", title: "我的点赞", showArrow: true) {
                handleMenuTap(title: "我的点赞")
            }

            createMenuItem(icon: "bookmark", title: "我的收藏", showArrow: true) {
                handleMenuTap(title: "我的收藏")
            }

            createMenuItem(icon: "clock", title: "浏览历史", showArrow: true) {
                handleMenuTap(title: "浏览历史")
            }

            createMenuItem(icon: "dollarsign.circle", title: "我的交易", showArrow: true) {
                handleMenuTap(title: "我的交易")
            }
        }
        .background(MHTheme.secondaryBackgroundColor)
        .cornerRadius(MHTheme.cornerRadiusL)
    }

    /// 创建设置区域
    /// - Returns: 设置视图
    private func createSettingsSection() -> some View {
        VStack(spacing: 0) {
            createMenuItem(icon: "bell", title: "通知设置", showArrow: true) {
                handleMenuTap(title: "通知设置")
            }

            createMenuItem(icon: "lock", title: "隐私设置", showArrow: true) {
                handleMenuTap(title: "隐私设置")
            }

            createMenuItem(icon: "questionmark.circle", title: "帮助与反馈", showArrow: true) {
                handleMenuTap(title: "帮助与反馈")
            }

            createMenuItem(icon: "info.circle", title: "关于我们", showArrow: true) {
                handleMenuTap(title: "关于我们")
            }
        }
        .background(MHTheme.secondaryBackgroundColor)
        .cornerRadius(MHTheme.cornerRadiusL)
    }

    /// 创建菜单项
    /// - Parameters:
    ///   - icon: 图标名称
    ///   - title: 标题
    ///   - showArrow: 是否显示箭头
    ///   - action: 点击动作
    /// - Returns: 菜单项视图
    private func createMenuItem(icon: String, title: String, showArrow: Bool = true, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(MHTheme.primaryColor)
                    .frame(width: 24, height: 24)

                Text(title)
                    .font(MHTheme.bodyFont)
                    .foregroundColor(MHTheme.primaryTextColor)

                Spacer()

                if showArrow {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14))
                        .foregroundColor(MHTheme.tertiaryTextColor)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(MHTheme.separatorColor)
                .padding(.leading, 52),
            alignment: .bottom
        )
    }

    /// 设置个人资料视图
    /// 在视图出现时进行必要的初始化操作
    private func setupProfileView() {
        // TODO: 初始化用户数据
        // 这里将来会调用ViewModel的初始化方法

        print("ProfileView初始化完成")
    }

    /// 处理统计项点击事件
    /// - Parameter title: 统计项标题
    private func handleStatTap(title: String) {
        // TODO: 实现统计项点击逻辑
        // 这里将来会调用Coordinator导航到对应页面

        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        print("点击了统计项: \(title)")
    }

    /// 处理菜单项点击事件
    /// - Parameter title: 菜单项标题
    private func handleMenuTap(title: String) {
        // TODO: 实现菜单项点击逻辑
        // 这里将来会调用Coordinator导航到对应页面

        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        print("点击了菜单项: \(title)")
    }
}

// MARK: - 预览
#Preview {
    ProfileView()
}
